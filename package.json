{"name": "personal-blog", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "date-fns": "^4.1.0", "github-markdown-css": "^5.8.1", "gray-matter": "^4.0.3", "next": "15.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.4.6", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "css-loader": "^7.1.2", "eslint": "^9", "eslint-config-next": "15.4.6", "less": "^4.4.0", "less-loader": "^12.3.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "^5"}}